export interface MCQOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

export interface MCQQuestion {
  id: string;
  question: string;
  options: MCQOption[];
  correctAnswer: string;
  difficulty: 'easy' | 'medium' | 'hard';
  topic: string;
  unit: number;
  bloomsLevel: 'remember' | 'understand' | 'apply' | 'analyze' | 'evaluate' | 'create';
  marks: number;
}

export interface QuestionPaper {
  id: string;
  title: string;
  courseTitle: string;
  courseCode: string;
  duration: number; // in minutes
  totalMarks: number;
  instructions: string[];
  questions: MCQQuestion[];
  selectedUnits: number[];
  createdAt: Date;
  generatedBy: string;
}

export interface QuestionGenerationRequest {
  courseId: string;
  courseTitle: string;
  courseCode: string;
  selectedUnits: number[];
  questionsPerUnit: number;
  totalQuestions: number;
  difficulty: 'mixed' | 'easy' | 'medium' | 'hard';
  duration: number;
  instructions?: string[];
}
