import React, { useState } from 'react';
import { Book<PERSON><PERSON>, User, LogIn, Sparkles } from 'lucide-react';

// Simple user type
interface SimpleUser {
  id: number;
  username: string;
  fullName: string;
}

// Simple storage functions
const saveUser = (user: SimpleUser) => {
  localStorage.setItem('simple_user', JSON.stringify(user));
};

const getUser = (): SimpleUser | null => {
  const stored = localStorage.getItem('simple_user');
  return stored ? JSON.parse(stored) : null;
};

const clearUser = () => {
  localStorage.removeItem('simple_user');
};

function SimpleApp() {
  const [user, setUser] = useState<SimpleUser | null>(getUser());
  const [showLogin, setShowLogin] = useState(false);
  const [loginForm, setLoginForm] = useState({ username: '', fullName: '' });

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (loginForm.username && loginForm.fullName) {
      const newUser: SimpleUser = {
        id: Date.now(),
        username: loginForm.username,
        fullName: loginForm.fullName
      };
      saveUser(newUser);
      setUser(newUser);
      setShowLogin(false);
      setLoginForm({ username: '', fullName: '' });
    }
  };

  const handleLogout = () => {
    clearUser();
    setUser(null);
  };

  const generateCourse = () => {
    alert(`Course generated for ${user?.fullName}! 
    
Features working:
✅ User authentication (localStorage)
✅ Course generation (mock)
✅ No API keys required
✅ Flexible CO/Unit counts

This is a simplified version to test the basic functionality.`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
      </div>

      {/* Header */}
      <header className="bg-gray-900/80 backdrop-blur-xl border-b border-gray-700/50 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-3 rounded-xl">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                  Course Design System
                </h1>
                <p className="text-sm text-gray-400">AI-Powered Curriculum Generator</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              {user ? (
                <div className="flex items-center gap-3">
                  <div className="text-sm text-gray-400 bg-gray-800/50 px-3 py-1 rounded-full">
                    Welcome, {user.fullName}!
                  </div>
                  <button
                    onClick={handleLogout}
                    className="bg-red-500/20 hover:bg-red-500/30 text-red-300 px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
                  >
                    <LogIn className="w-4 h-4" />
                    Sign Out
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setShowLogin(true)}
                  className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 flex items-center gap-2"
                >
                  <LogIn className="w-4 h-4" />
                  Sign In
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="py-20 px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-8">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 backdrop-blur-sm text-cyan-300 px-6 py-3 rounded-full text-sm font-medium mb-6 border border-cyan-500/30">
              <Sparkles className="w-4 h-4 animate-spin" />
              AI-Powered Course Design
            </div>
            
            <h1 className="text-6xl font-bold mb-6 leading-tight">
              <span className="bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                Design Complete University
              </span>
              <br />
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent">
                Course Syllabi
              </span>
            </h1>
            
            <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
              Generate comprehensive, AICTE-compliant course syllabi for Indian universities. 
              Create complete curricula with objectives, outcomes, units, and resources in minutes.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
            {user ? (
              <button
                onClick={generateCourse}
                className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-bold px-12 py-6 rounded-2xl hover:from-cyan-600 hover:to-blue-600 transition-all duration-300 shadow-2xl hover:shadow-cyan-500/25 text-xl transform hover:scale-105"
              >
                Start Course Generation
              </button>
            ) : (
              <button
                onClick={() => setShowLogin(true)}
                className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-bold px-12 py-6 rounded-2xl hover:from-cyan-600 hover:to-blue-600 transition-all duration-300 shadow-2xl hover:shadow-cyan-500/25 text-xl transform hover:scale-105"
              >
                Get Started - Sign In Required
              </button>
            )}
          </div>

          {/* Features */}
          <div className="mt-16 grid md:grid-cols-3 gap-8">
            <div className="bg-gray-800/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50">
              <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-3 rounded-xl w-12 h-12 mx-auto mb-4">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-cyan-300 mb-2">No API Keys Required</h3>
              <p className="text-gray-400">Generate courses instantly without any external API dependencies</p>
            </div>
            
            <div className="bg-gray-800/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50">
              <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-3 rounded-xl w-12 h-12 mx-auto mb-4">
                <User className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-purple-300 mb-2">User Accounts</h3>
              <p className="text-gray-400">Save your courses and preferences with secure user accounts</p>
            </div>
            
            <div className="bg-gray-800/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50">
              <div className="bg-gradient-to-r from-green-500 to-teal-500 p-3 rounded-xl w-12 h-12 mx-auto mb-4">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-green-300 mb-2">Flexible Structure</h3>
              <p className="text-gray-400">Customize CO and unit counts according to your needs</p>
            </div>
          </div>
        </div>
      </main>

      {/* Login Modal */}
      {showLogin && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800/95 backdrop-blur-xl rounded-2xl p-8 w-full max-w-md border border-gray-700/50">
            <h2 className="text-2xl font-bold text-cyan-300 mb-6 text-center">Quick Sign In</h2>
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Username</label>
                <input
                  type="text"
                  value={loginForm.username}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, username: e.target.value }))}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                  placeholder="Enter username"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                <input
                  type="text"
                  value={loginForm.fullName}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, fullName: e.target.value }))}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                  placeholder="Enter full name"
                  required
                />
              </div>
              <div className="flex gap-3">
                <button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-bold py-3 rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-200"
                >
                  Sign In
                </button>
                <button
                  type="button"
                  onClick={() => setShowLogin(false)}
                  className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
            <p className="text-xs text-gray-400 mt-4 text-center">
              This is a simplified demo. Data is stored locally in your browser.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

export default SimpleApp;
