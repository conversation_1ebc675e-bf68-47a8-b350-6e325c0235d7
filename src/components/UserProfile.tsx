import React, { useState, useEffect } from 'react';
import { User, Settings, LogOut, BookOpen, Save, Plus, Minus, LogIn } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import databaseService from '../services/databaseService';

interface UserProfileProps {
  isOpen: boolean;
  onClose: () => void;
  onShowAuth?: () => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({ isOpen, onClose, onShowAuth }) => {
  const { user, logout, updatePreferences, getUserPreferences, isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'preferences' | 'courses'>('profile');
  const [preferences, setPreferences] = useState({ cosCount: 5, unitsCount: 5 });
  const [savedCourses, setSavedCourses] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (isOpen && user) {
      loadUserData();
    }
  }, [isOpen, user]);

  const loadUserData = async () => {
    try {
      const prefs = await getUserPreferences();
      setPreferences(prefs);
      
      const courses = await databaseService.getUserCourses(user!.id);
      setSavedCourses(courses);
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const handleSavePreferences = async () => {
    setIsLoading(true);
    try {
      const success = await updatePreferences(preferences.cosCount, preferences.unitsCount);
      if (success) {
        setMessage('Preferences saved successfully!');
        setTimeout(() => setMessage(''), 3000);
      } else {
        setMessage('Failed to save preferences');
      }
    } catch (error) {
      setMessage('Error saving preferences');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
    onClose();
  };

  const handleShowLogin = () => {
    onClose();
    if (onShowAuth) {
      onShowAuth();
    }
  };

  if (!isOpen) return null;

  // Show login prompt for non-authenticated users
  if (!isAuthenticated || !user) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
        <div className="bg-gray-900/95 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 max-w-md w-full shadow-2xl">
          <div className="text-center">
            {/* Header with close button */}
            <div className="flex justify-end mb-4">
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800"
              >
                ✕
              </button>
            </div>

            <div className="bg-gradient-to-r from-cyan-500 to-blue-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
              <User className="w-8 h-8 text-white" />
            </div>

            <h2 className="text-2xl font-bold text-white mb-4">Welcome to CourseCraft</h2>
            <p className="text-gray-400 mb-8">
              Sign in to access your profile, save courses, and customize your preferences.
            </p>

            <div className="space-y-4">
              <button
                onClick={handleShowLogin}
                className="w-full bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-bold py-4 px-6 rounded-xl hover:from-cyan-600 hover:to-blue-600 transition-all duration-300 flex items-center justify-center gap-3 shadow-lg"
              >
                <LogIn className="w-5 h-5" />
                Sign In / Register
              </button>

              <button
                onClick={onClose}
                className="w-full bg-gray-800/50 text-gray-300 font-medium py-3 px-6 rounded-xl hover:bg-gray-700/50 transition-all duration-300 border border-gray-600/50"
              >
                Continue as Guest
              </button>
            </div>

            <div className="mt-6 p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
              <p className="text-sm text-blue-300 font-medium mb-2">Benefits of signing in:</p>
              <ul className="text-xs text-gray-400 space-y-1">
                <li>• Save and manage your courses</li>
                <li>• Customize CO and unit counts</li>
                <li>• Access course history</li>
                <li>• Export professional PDFs</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900/95 backdrop-blur-xl rounded-2xl shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden border border-gray-700/50">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-3 rounded-xl">
              <User className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-cyan-300">User Profile</h2>
              <p className="text-sm text-gray-400">Welcome back, {user.fullName}!</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handleLogout}
              className="bg-red-500/20 hover:bg-red-500/30 text-red-300 px-4 py-2 rounded-lg transition-all duration-200 text-sm font-medium flex items-center gap-2"
            >
              <LogOut className="w-4 h-4" />
              Sign Out
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700/50">
          {[
            { id: 'profile', label: 'Profile', icon: User },
            { id: 'preferences', label: 'Preferences', icon: Settings },
            { id: 'courses', label: 'My Courses', icon: BookOpen }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-cyan-300 border-b-2 border-cyan-500 bg-gray-800/50'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {message && (
            <div className="bg-green-500/20 border border-green-500/50 rounded-lg p-3 mb-6">
              <p className="text-green-300 text-sm">{message}</p>
            </div>
          )}

          {activeTab === 'profile' && (
            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-gray-800/40 rounded-xl p-6 border border-gray-700/50">
                  <h3 className="font-semibold text-cyan-300 mb-4">Personal Information</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm text-gray-400">Full Name</label>
                      <p className="text-white">{user.fullName}</p>
                    </div>
                    <div>
                      <label className="text-sm text-gray-400">Username</label>
                      <p className="text-white">{user.username}</p>
                    </div>
                    <div>
                      <label className="text-sm text-gray-400">Email</label>
                      <p className="text-white">{user.email}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-800/40 rounded-xl p-6 border border-gray-700/50">
                  <h3 className="font-semibold text-cyan-300 mb-4">Professional Information</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm text-gray-400">Department</label>
                      <p className="text-white">{user.department}</p>
                    </div>
                    <div>
                      <label className="text-sm text-gray-400">Designation</label>
                      <p className="text-white">{user.designation}</p>
                    </div>
                    <div>
                      <label className="text-sm text-gray-400">Institution</label>
                      <p className="text-white">{user.institution}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800/40 rounded-xl p-6 border border-gray-700/50">
                <h3 className="font-semibold text-cyan-300 mb-4">Account Information</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="text-sm text-gray-400">Member Since</label>
                    <p className="text-white">{new Date(user.createdAt).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Last Login</label>
                    <p className="text-white">
                      {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="space-y-6">
              <div className="bg-gray-800/40 rounded-xl p-6 border border-gray-700/50">
                <h3 className="font-semibold text-cyan-300 mb-4">Course Generation Preferences</h3>
                <p className="text-gray-400 mb-6">
                  Set your default preferences for course generation. You can always modify these when creating a course.
                </p>

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      Default Number of Course Outcomes (COs)
                    </label>
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => setPreferences(prev => ({ 
                          ...prev, 
                          cosCount: Math.max(1, prev.cosCount - 1) 
                        }))}
                        className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors"
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <div className="bg-gray-700/50 px-6 py-2 rounded-lg border border-gray-600">
                        <span className="text-xl font-bold text-cyan-300">{preferences.cosCount}</span>
                      </div>
                      <button
                        onClick={() => setPreferences(prev => ({ 
                          ...prev, 
                          cosCount: Math.min(10, prev.cosCount + 1) 
                        }))}
                        className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                      <span className="text-gray-400 text-sm">Course Outcomes</span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      Default Number of Units
                    </label>
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => setPreferences(prev => ({ 
                          ...prev, 
                          unitsCount: Math.max(1, prev.unitsCount - 1) 
                        }))}
                        className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors"
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <div className="bg-gray-700/50 px-6 py-2 rounded-lg border border-gray-600">
                        <span className="text-xl font-bold text-cyan-300">{preferences.unitsCount}</span>
                      </div>
                      <button
                        onClick={() => setPreferences(prev => ({ 
                          ...prev, 
                          unitsCount: Math.min(10, prev.unitsCount + 1) 
                        }))}
                        className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg transition-colors"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                      <span className="text-gray-400 text-sm">Course Units</span>
                    </div>
                  </div>

                  <button
                    onClick={handleSavePreferences}
                    disabled={isLoading}
                    className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-6 py-3 rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-200 disabled:opacity-50 flex items-center gap-2"
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        Save Preferences
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'courses' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-cyan-300">My Saved Courses</h3>
                <span className="text-sm text-gray-400">{savedCourses.length} courses saved</span>
              </div>

              {savedCourses.length === 0 ? (
                <div className="text-center py-12">
                  <BookOpen className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-400 mb-2">No courses saved yet</h4>
                  <p className="text-gray-500">Start generating courses to see them here!</p>
                </div>
              ) : (
                <div className="grid gap-4">
                  {savedCourses.map((course: any, index) => (
                    <div key={index} className="bg-gray-800/40 rounded-xl p-4 border border-gray-700/50">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-white">{course.title}</h4>
                          <p className="text-sm text-gray-400">
                            {course.code} • {course.credits} Credits
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-400">
                            Saved: {new Date(course.savedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-700/50 p-4 bg-gray-800/30">
          <div className="text-center text-sm text-gray-400">
            Manage your preferences and saved courses above
          </div>
        </div>
      </div>
    </div>
  );
};
