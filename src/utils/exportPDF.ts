import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Course } from '../types/course';

export const exportToPDF = async (course: Course, filename: string) => {
  try {
    // Use the actual course data instead of parsing from DOM
    const courseData = {
      title: course.title,
      code: course.code,
      credits: course.credits.toString(),
      department: course.department || 'Computer Science',
      semester: course.semester || '3',
      outcomes: course.outcomes,
      units: course.units,
      textbooks: course.textBooks,
      references: course.referenceBooks
    };
    
    // Create PDF with professional formatting
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 15;
    
    let yPosition = margin;

    // Add header with logo and title
    addHeader(pdf, courseData, margin, yPosition);
    yPosition += 30;
    
    // Add course information table
    yPosition = addCourseInfoTable(pdf, courseData, margin, yPosition);
    yPosition += 10;
    
    // Add course outcomes table
    yPosition = addCourseOutcomesTable(pdf, courseData, margin, yPosition);
    yPosition += 10;
    
    // Add units table
    yPosition = addUnitsTable(pdf, courseData, margin, yPosition);
    yPosition += 10;
    
    // Add assessment pattern table
    yPosition = addAssessmentTable(pdf, courseData, margin, yPosition);
    yPosition += 10;
    
    // Add textbooks table
    yPosition = addTextbooksTable(pdf, courseData, margin, yPosition);
    
    // Add page numbers
    addPageNumbers(pdf);
    
    // Save the PDF
    pdf.save(filename);
    
  } catch (error) {
    console.error('Error exporting PDF:', error);
    throw new Error('Failed to export PDF. Please try again.');
  }
};



// Function to add header
function addHeader(pdf: jsPDF, courseData: any, margin: number, y: number) {
  // Add CourseCraft logo/title
  pdf.setFontSize(16);
  pdf.setTextColor(0, 102, 204); // Blue color
  pdf.text('CourseCraft', margin, y);
  
  pdf.setFontSize(10);
  pdf.setTextColor(100, 100, 100);
  pdf.text('AI-Powered Curriculum Generator', margin, y + 6);
  
  // Add course title
  pdf.setFontSize(18);
  pdf.setTextColor(0, 0, 0);
  const title = courseData.title || 'Course Syllabus';
  pdf.text(title, margin, y + 20);
}

// Function to add course information table
function addCourseInfoTable(pdf: jsPDF, courseData: any, margin: number, y: number) {
  const tableData = [
    ['Course Code', courseData.code || 'CS301'],
    ['Course Title', courseData.title || 'Course Title'],
    ['Credits', courseData.credits || '3'],
    ['Department', courseData.department || 'Computer Science'],
    ['Semester', courseData.semester || '3']
  ];
  
  autoTable(pdf, {
    startY: y,
    head: [['Course Information', '']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: [0, 102, 204],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 10,
      cellPadding: 3
    },
    columnStyles: {
      0: { cellWidth: 60, fontStyle: 'bold' },
      1: { cellWidth: 120 }
    },
    margin: { left: margin, right: margin }
  });
  
  return (pdf as any).lastAutoTable.finalY + 10;
}

// Function to add course outcomes table
function addCourseOutcomesTable(pdf: jsPDF, courseData: any, margin: number, y: number) {
  // Use the actual course outcomes from the generated course
  const outcomes = courseData.outcomes || [];

  // Create table data with each outcome in its own row
  const tableData = outcomes.map((outcome: any) => [
    outcome.id,
    outcome.description
  ]);

  autoTable(pdf, {
    startY: y,
    head: [['Course Outcome', 'Learning Objective Description']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: [0, 102, 204],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 9,
      cellPadding: 4,
      valign: 'top'
    },
    columnStyles: {
      0: {
        cellWidth: 25,
        halign: 'center',
        fontStyle: 'bold',
        fillColor: [240, 248, 255]
      },
      1: {
        cellWidth: 155,
        cellPadding: 5
      }
    },
    margin: { left: margin, right: margin },
    styles: {
      cellPadding: 4,
      fontSize: 9,
      lineColor: [200, 200, 200],
      lineWidth: 0.5
    }
  });

  return (pdf as any).lastAutoTable.finalY + 10;
}

// Function to add units table
function addUnitsTable(pdf: jsPDF, courseData: any, margin: number, y: number) {
  // Use the actual course units from the generated course
  const units = courseData.units || [];

  // Create table data with each unit in its own row
  const tableData = units.map((unit: any) => [
    `Unit ${unit.unitNumber}`,
    unit.title,
    formatTopicsList(unit.topics)
  ]);

  autoTable(pdf, {
    startY: y,
    head: [['Unit', 'Unit Title', 'Topics Covered']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: [0, 102, 204],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 9,
      cellPadding: 4,
      valign: 'top'
    },
    columnStyles: {
      0: {
        cellWidth: 18,
        halign: 'center',
        fontStyle: 'bold',
        fillColor: [240, 248, 255]
      },
      1: {
        cellWidth: 52,
        fontStyle: 'bold',
        cellPadding: 4
      },
      2: {
        cellWidth: 110,
        cellPadding: 4
      }
    },
    margin: { left: margin, right: margin },
    styles: {
      cellPadding: 4,
      fontSize: 9,
      lineColor: [200, 200, 200],
      lineWidth: 0.5
    }
  });

  return (pdf as any).lastAutoTable.finalY + 10;
}

// Helper function to format topics list
function formatTopicsList(topics: string[]): string {
  return topics.map((topic, index) => `${index + 1}. ${topic}`).join('\n');
}

// Function to add assessment pattern table
function addAssessmentTable(pdf: jsPDF, courseData: any, margin: number, y: number) {
  const assessmentData = [
    ['Internal Assessment', '40%'],
    ['Mid-term Examination', '20%'],
    ['End-term Examination', '60%'],
    ['Total', '100%']
  ];

  autoTable(pdf, {
    startY: y,
    head: [['Assessment Component', 'Weightage']],
    body: assessmentData,
    theme: 'grid',
    headStyles: {
      fillColor: [0, 102, 204],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 10,
      cellPadding: 3
    },
    columnStyles: {
      0: { cellWidth: 120, fontStyle: 'bold' },
      1: { cellWidth: 60, halign: 'center' }
    },
    margin: { left: margin, right: margin }
  });

  return (pdf as any).lastAutoTable.finalY + 10;
}

// Function to add textbooks table
function addTextbooksTable(pdf: jsPDF, courseData: any, margin: number, y: number) {
  // Use actual textbooks from the course data
  const textbooks = courseData.textbooks || [];
  const references = courseData.references || [];

  // Format textbooks data
  const textbooksData = textbooks.map((book: any, index: number) => [
    (index + 1).toString(),
    book.title || book,
    book.author || 'Various Authors',
    book.publisher || 'Academic Press',
    book.year || '2023'
  ]);

  // Format references data
  const referencesData = references.map((ref: any, index: number) => [
    (index + 1).toString(),
    ref.title || ref,
    ref.author || 'Various Authors',
    ref.publisher || 'Academic Press',
    ref.year || '2023'
  ]);

  // Add textbooks table
  autoTable(pdf, {
    startY: y,
    head: [['S.No', 'Title', 'Author', 'Publisher', 'Year']],
    body: textbooksData,
    theme: 'grid',
    headStyles: {
      fillColor: [0, 102, 204],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 9,
      cellPadding: 3
    },
    columnStyles: {
      0: { cellWidth: 15, halign: 'center' },
      1: { cellWidth: 70 },
      2: { cellWidth: 40 },
      3: { cellWidth: 35 },
      4: { cellWidth: 20, halign: 'center' }
    },
    margin: { left: margin, right: margin }
  });

  let newY = (pdf as any).lastAutoTable.finalY + 10;

  // Add references table
  autoTable(pdf, {
    startY: newY,
    head: [['S.No', 'Reference Title', 'Author', 'Publisher', 'Year']],
    body: referencesData,
    theme: 'grid',
    headStyles: {
      fillColor: [102, 102, 102],
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 9,
      cellPadding: 3
    },
    columnStyles: {
      0: { cellWidth: 15, halign: 'center' },
      1: { cellWidth: 70 },
      2: { cellWidth: 40 },
      3: { cellWidth: 35 },
      4: { cellWidth: 20, halign: 'center' }
    },
    margin: { left: margin, right: margin }
  });

  return (pdf as any).lastAutoTable.finalY + 10;
}

// Function to add page numbers
function addPageNumbers(pdf: jsPDF) {
  const totalPages = pdf.getNumberOfPages();
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();

  for (let i = 1; i <= totalPages; i++) {
    pdf.setPage(i);
    pdf.setFontSize(8);
    pdf.setTextColor(100, 100, 100);
    pdf.text(`Page ${i} of ${totalPages}`, pageWidth - 30, pageHeight - 10);
  }
}
