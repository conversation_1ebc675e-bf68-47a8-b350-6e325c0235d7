import { LLMService, LLMConfig } from './llmService';
import { ChatMessage } from './chatbotService';

// LangChain imports for enhanced chatbot features
import { ConversationChain } from 'langchain/chains';
import { BufferMemory, ConversationSummaryMemory } from 'langchain/memory';
import { PromptTemplate } from '@langchain/core/prompts';
import { BaseLanguageModel } from '@langchain/core/language_models/base';
import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';

export interface EnhancedChatbotConfig {
  llmConfig: LLMConfig;
  memoryType?: 'buffer' | 'summary';
  maxTokenLimit?: number;
  conversationSummaryTokens?: number;
}

export class LangChainChatbotService {
  private llm: BaseLanguageModel;
  private memory: BufferMemory | ConversationSummaryMemory;
  private conversationChain: ConversationChain;
  private config: EnhancedChatbotConfig;

  constructor(config: EnhancedChatbotConfig) {
    this.config = {
      memoryType: 'buffer',
      maxTokenLimit: 4000,
      conversationSummaryTokens: 1000,
      ...config
    };

    // Initialize LLM
    this.llm = this.initializeLLM();

    // Initialize memory
    this.memory = this.initializeMemory();

    // Initialize conversation chain
    this.conversationChain = this.initializeConversationChain();
  }

  private initializeLLM(): BaseLanguageModel {
    const { provider, apiKey, model, temperature = 0.7, maxTokens = 4000 } = this.config.llmConfig;

    switch (provider) {
      case 'openai':
        return new ChatOpenAI({
          openAIApiKey: apiKey,
          modelName: model || 'gpt-4-turbo-preview',
          temperature,
          maxTokens,
        });

      case 'anthropic':
        return new ChatAnthropic({
          anthropicApiKey: apiKey,
          modelName: model || 'claude-3-sonnet-20240229',
          temperature,
          maxTokens,
        });

      case 'google':
        return new ChatGoogleGenerativeAI({
          apiKey: apiKey,
          modelName: model || 'gemini-pro',
          temperature,
          maxTokens,
        });

      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  private initializeMemory(): BufferMemory | ConversationSummaryMemory {
    if (this.config.memoryType === 'summary') {
      return new ConversationSummaryMemory({
        llm: this.llm,
        maxTokenLimit: this.config.conversationSummaryTokens,
        returnMessages: true,
      });
    } else {
      return new BufferMemory({
        returnMessages: true,
        memoryKey: 'history',
      });
    }
  }

  private initializeConversationChain(): ConversationChain {
    const prompt = PromptTemplate.fromTemplate(`
You are a professional Course Design Assistant specializing in Indian higher education. Your expertise includes:

**Core Competencies:**
- AICTE (All India Council for Technical Education) guidelines and compliance
- UGC (University Grants Commission) standards and frameworks
- NEP 2020 (National Education Policy) implementation
- Bloom's Taxonomy and learning outcome design
- Indian university credit systems and assessment patterns
- Curriculum design best practices
- Industry-academia alignment
- Academic quality assurance

**Communication Style:**
- Professional and knowledgeable
- Provide specific, actionable advice
- Reference official guidelines when relevant
- Use clear, academic language
- Be helpful and supportive
- Acknowledge limitations when appropriate

**Key Areas of Assistance:**
1. Course structure and credit allocation
2. Learning objectives and outcomes design
3. Assessment pattern recommendations
4. AICTE compliance requirements
5. Unit planning and topic organization
6. Textbook and resource selection
7. Industry relevance and skill development
8. Academic calendar and scheduling

**Response Guidelines:**
- Keep responses concise but comprehensive
- Provide practical examples when helpful
- Reference specific AICTE/UGC guidelines when applicable
- Suggest next steps or follow-up actions
- Maintain professional academic tone
- If unsure about specific regulations, recommend consulting official sources

Current conversation:
{history}

Human: {input}
Assistant:`);

    return new ConversationChain({
      llm: this.llm,
      memory: this.memory,
      prompt,
      verbose: false,
    });
  }

  async generateResponse(userMessage: string): Promise<string> {
    try {
      const response = await this.conversationChain.call({
        input: userMessage,
      });

      return response.response || response.text || 'I apologize, but I encountered an issue generating a response. Please try again.';
    } catch (error) {
      console.error('Error generating LangChain response:', error);

      // Fallback to a simple response
      return this.generateFallbackResponse(userMessage);
    }
  }

  private generateFallbackResponse(userMessage: string): string {
    const lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
      return 'Hello! I\'m your Course Design Assistant. I can help you with AICTE guidelines, curriculum design, learning outcomes, assessment patterns, and provide website links and textbook recommendations. What would you like to know?';
    }

    if (lowerMessage.includes('help')) {
      return 'I can assist you with:\n• Course design and AICTE compliance\n• Learning outcomes and assessment patterns\n• Website links and online resources\n• Textbook recommendations\n• Curriculum development\n\nWhat specific topic would you like to explore?';
    }

    // Check for resource requests
    if (lowerMessage.includes('website') || lowerMessage.includes('link') || lowerMessage.includes('resource')) {
      return this.getResourceRecommendations(userMessage);
    }

    if (lowerMessage.includes('textbook') || lowerMessage.includes('book') || lowerMessage.includes('reference')) {
      return this.getTextbookRecommendations(userMessage);
    }

    // Subject-specific responses
    if (lowerMessage.includes('machine learning') || lowerMessage.includes('ml')) {
      return this.getMachineLearningResources();
    }

    if (lowerMessage.includes('data structure') || lowerMessage.includes('dsa')) {
      return this.getDataStructuresResources();
    }

    if (lowerMessage.includes('database') || lowerMessage.includes('dbms')) {
      return this.getDatabaseResources();
    }

    if (lowerMessage.includes('network') || lowerMessage.includes('networking')) {
      return this.getNetworkingResources();
    }

    return 'I\'m here to help with course design and curriculum development. I can provide website links, textbook recommendations, and guidance on AICTE compliance. Could you please ask about a specific subject or topic?';
  }

  private getResourceRecommendations(userMessage: string): string {
    const lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.includes('machine learning') || lowerMessage.includes('ml')) {
      return this.getMachineLearningResources();
    }

    if (lowerMessage.includes('data structure')) {
      return this.getDataStructuresResources();
    }

    return `Here are some excellent educational websites for course development:

**General Programming & CS:**
• [Coursera](https://www.coursera.org) - University-level courses
• [edX](https://www.edx.org) - MIT, Harvard courses
• [Khan Academy](https://www.khanacademy.org) - Free learning platform

**Technical Resources:**
• [GeeksforGeeks](https://www.geeksforgeeks.org) - Programming tutorials
• [Stack Overflow](https://stackoverflow.com) - Developer community
• [GitHub](https://github.com) - Code repositories and projects

**Academic Resources:**
• [IEEE Xplore](https://ieeexplore.ieee.org) - Research papers
• [ACM Digital Library](https://dl.acm.org) - Computer science research
• [Google Scholar](https://scholar.google.com) - Academic search

Which specific subject would you like detailed resources for?`;
  }

  private getTextbookRecommendations(userMessage: string): string {
    const lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.includes('machine learning') || lowerMessage.includes('ml')) {
      return `**Machine Learning Textbooks:**

**Core Textbooks:**
• "Pattern Recognition and Machine Learning" by Christopher Bishop
• "The Elements of Statistical Learning" by Hastie, Tibshirani & Friedman
• "Machine Learning: A Probabilistic Perspective" by Kevin Murphy

**Practical Books:**
• "Hands-On Machine Learning" by Aurélien Géron
• "Python Machine Learning" by Sebastian Raschka
• "Introduction to Statistical Learning" by James, Witten, Hastie & Tibshirani

**Advanced References:**
• "Deep Learning" by Ian Goodfellow, Yoshua Bengio & Aaron Courville
• "Reinforcement Learning: An Introduction" by Sutton & Barto

Would you like specific ISBN numbers or more details about any of these books?`;
    }

    return `**General Computer Science Textbooks:**

**Programming Fundamentals:**
• "Introduction to Algorithms" by Cormen, Leiserson, Rivest & Stein
• "Clean Code" by Robert C. Martin
• "Design Patterns" by Gang of Four

**Database Systems:**
• "Database System Concepts" by Silberschatz, Korth & Sudarshan
• "Fundamentals of Database Systems" by Elmasri & Navathe

**Computer Networks:**
• "Computer Networking: A Top-Down Approach" by Kurose & Ross
• "Computer Networks" by Andrew Tanenbaum

Which specific subject textbooks would you like recommendations for?`;
  }

  private getMachineLearningResources(): string {
    return `**Machine Learning Resources:**

**🌐 Essential Websites:**
• [Coursera ML Course](https://www.coursera.org/learn/machine-learning) - Andrew Ng's famous course
• [Kaggle Learn](https://www.kaggle.com/learn) - Free micro-courses
• [Fast.ai](https://www.fast.ai) - Practical deep learning
• [Scikit-learn](https://scikit-learn.org/stable/tutorial/index.html) - Official tutorials
• [TensorFlow](https://www.tensorflow.org/tutorials) - Google's ML platform
• [PyTorch](https://pytorch.org/tutorials/) - Facebook's ML framework

**📚 Recommended Textbooks:**
• "Pattern Recognition and Machine Learning" - Christopher Bishop
• "The Elements of Statistical Learning" - Hastie, Tibshirani & Friedman
• "Hands-On Machine Learning" - Aurélien Géron
• "Python Machine Learning" - Sebastian Raschka

**🎯 Practical Resources:**
• [Google Colab](https://colab.research.google.com) - Free GPU/TPU access
• [Jupyter Notebooks](https://jupyter.org) - Interactive development
• [Papers with Code](https://paperswithcode.com) - Latest research implementations

Would you like more specific resources for any ML subtopic?`;
  }

  private getDataStructuresResources(): string {
    return `**Data Structures & Algorithms Resources:**

**🌐 Learning Websites:**
• [GeeksforGeeks](https://www.geeksforgeeks.org/data-structures/) - Comprehensive tutorials
• [LeetCode](https://leetcode.com/explore/learn/) - Practice problems
• [HackerRank](https://www.hackerrank.com/domains/data-structures) - Coding challenges
• [VisuAlgo](https://visualgo.net/en) - Algorithm visualizations
• [CS50](https://cs50.harvard.edu/x/2023/) - Harvard's intro course

**📚 Essential Textbooks:**
• "Introduction to Algorithms" - Cormen, Leiserson, Rivest & Stein (CLRS)
• "Data Structures and Algorithms in Java" - Robert Lafore
• "Algorithms" - Robert Sedgewick & Kevin Wayne
• "Data Structures and Algorithm Analysis" - Mark Allen Weiss

**🎯 Practice Platforms:**
• [Codeforces](https://codeforces.com) - Competitive programming
• [AtCoder](https://atcoder.jp) - Algorithm contests
• [TopCoder](https://www.topcoder.com) - Programming competitions

Need help with specific data structures or algorithms?`;
  }

  private getDatabaseResources(): string {
    return `**Database Management Resources:**

**🌐 Learning Websites:**
• [W3Schools SQL](https://www.w3schools.com/sql/) - SQL tutorial
• [SQLBolt](https://sqlbolt.com/) - Interactive SQL lessons
• [PostgreSQL Tutorial](https://www.postgresql.org/docs/current/tutorial.html) - Official docs
• [MySQL Tutorial](https://dev.mysql.com/doc/mysql-tutorial-excerpt/8.0/en/) - MySQL guide
• [MongoDB University](https://university.mongodb.com/) - NoSQL database

**📚 Recommended Textbooks:**
• "Database System Concepts" - Silberschatz, Korth & Sudarshan
• "Fundamentals of Database Systems" - Elmasri & Navathe
• "Database Management Systems" - Raghu Ramakrishnan
• "SQL in 10 Minutes" - Ben Forta

**🎯 Practical Tools:**
• [phpMyAdmin](https://www.phpmyadmin.net/) - MySQL administration
• [pgAdmin](https://www.pgadmin.org/) - PostgreSQL administration
• [MongoDB Compass](https://www.mongodb.com/products/compass) - MongoDB GUI

Would you like specific database design or SQL query help?`;
  }

  private getNetworkingResources(): string {
    return `**Computer Networks Resources:**

**🌐 Learning Websites:**
• [Cisco Networking Academy](https://www.netacad.com/) - CCNA courses
• [Wireshark University](https://www.wireshark.org/docs/) - Network analysis
• [Packet Tracer](https://www.netacad.com/courses/packet-tracer) - Network simulation
• [Khan Academy Networking](https://www.khanacademy.org/computing/computers-and-internet) - Basics

**📚 Essential Textbooks:**
• "Computer Networking: A Top-Down Approach" - Kurose & Ross
• "Computer Networks" - Andrew Tanenbaum
• "Network+ Guide to Networks" - Tamara Dean
• "TCP/IP Illustrated" - W. Richard Stevens

**🎯 Practical Tools:**
• [Wireshark](https://www.wireshark.org/) - Network protocol analyzer
• [Nmap](https://nmap.org/) - Network discovery tool
• [GNS3](https://www.gns3.com/) - Network emulator

Need help with specific networking protocols or concepts?`;
  }

  async clearMemory(): Promise<void> {
    await this.memory.clear();
  }

  async getMemoryVariables(): Promise<Record<string, any>> {
    return await this.memory.loadMemoryVariables({});
  }

  // Method to get conversation summary (useful for summary memory)
  async getConversationSummary(): Promise<string> {
    if (this.memory instanceof ConversationSummaryMemory) {
      const variables = await this.memory.loadMemoryVariables({});
      return variables.history || 'No conversation history available.';
    } else {
      const variables = await this.memory.loadMemoryVariables({});
      return `Recent conversation: ${variables.history || 'No history available.'}`;
    }
  }

  // Method to export conversation for analysis
  async exportConversation(): Promise<ChatMessage[]> {
    const variables = await this.memory.loadMemoryVariables({});
    const history = variables.history || '';

    // Parse the history into ChatMessage format
    const messages: ChatMessage[] = [];
    const lines = history.split('\n').filter(line => line.trim());

    lines.forEach((line, index) => {
      if (line.startsWith('Human:')) {
        messages.push({
          id: `msg_${index}`,
          text: line.replace('Human:', '').trim(),
          sender: 'user',
          timestamp: new Date(),
        });
      } else if (line.startsWith('Assistant:')) {
        messages.push({
          id: `msg_${index}`,
          text: line.replace('Assistant:', '').trim(),
          sender: 'bot',
          timestamp: new Date(),
        });
      }
    });

    return messages;
  }
}